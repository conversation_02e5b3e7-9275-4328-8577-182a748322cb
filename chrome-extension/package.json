{"name": "frontend-development-mcp-extension", "version": "1.2.0", "description": "Chrome extension for Frontend Development MCP integration", "main": "background.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["chrome-extension", "mcp", "frontend-development"], "author": "Winds AI", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/Winds-AI/Frontend-development-mcp-tools"}, "inspiredBy": "https://github.com/AgentDeskAI/browser-tools-mcp"}