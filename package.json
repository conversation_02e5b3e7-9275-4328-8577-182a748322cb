{"name": "@winds-ai/frontend-development-mcp-tools", "version": "1.2.2", "description": "Complete MCP (Model Context Protocol) toolkit for browser-based frontend development with AI IDEs", "type": "module", "main": "browser-tools-mcp/dist/mcp-server.js", "bin": {"frontend-development-mcp": "./browser-tools-mcp/dist/mcp-server.js", "frontend-development-mcp-server": "./browser-tools-server/dist/browser-connector.js", "frontend-dev-mcp": "./scripts/start-all.js"}, "scripts": {"install-all": "npm run install:mcp && npm run install:server", "install:mcp": "cd browser-tools-mcp && npm install", "install:server": "cd browser-tools-server && npm install", "build": "npm run build:mcp && npm run build:server", "build:mcp": "cd browser-tools-mcp && npm run build", "build:server": "cd browser-tools-server && npm run build", "start": "npm run build && npm run start:server", "start:server": "cd browser-tools-server && npm start", "start:mcp": "cd browser-tools-mcp && npm start", "start:all": "node scripts/start-all.js", "postinstall": "node scripts/global-install.js 2>/dev/null || echo 'Local installation - use npm run setup for development'", "prepublishOnly": "npm run build", "setup": "npm run install-all && npm run build && echo '\n✅ Setup complete! Run \"npm start\" to start the server.'", "dev": "npm run start:all", "test": "echo 'Testing connection...' && curl -f http://localhost:3025/connection-health || curl -f http://localhost:3026/connection-health || echo 'Server not running. Run \"npm start\" first.'"}, "keywords": ["mcp", "model-context-protocol", "browser", "tools", "debugging", "ai", "chrome", "extension", "frontend", "development", "windsurf", "cursor", "claude"], "author": "Winds AI", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/Winds-AI/Frontend-development-mcp-tools"}, "homepage": "https://github.com/Winds-AI/Frontend-development-mcp-tools#readme", "bugs": {"url": "https://github.com/Winds-AI/Frontend-development-mcp-tools/issues"}, "engines": {"node": ">=18.0.0"}, "workspaces": ["browser-tools-mcp", "browser-tools-server"], "dependencies": {"@modelcontextprotocol/sdk": "^1.4.1", "body-parser": "^1.20.3", "cors": "^2.8.5", "express": "^4.21.2", "node-fetch": "^2.7.0", "ws": "^8.18.1"}, "devDependencies": {"@types/node": "^22.13.1", "typescript": "^5.7.3"}, "files": ["browser-tools-mcp/dist/**/*", "browser-tools-server/dist/**/*", "chrome-extension/**/*", "scripts/**/*", "docs/**/*", "README.md", "SETUP_GUIDE.md"]}