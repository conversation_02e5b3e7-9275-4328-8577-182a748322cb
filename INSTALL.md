# Frontend Development MCP Tools - Installation Guide

## 🚀 One-Command Installation

### Option 1: Global Installation (Recommended for End Users)

```bash
npm install -g @winds-ai/frontend-development-mcp-tools
```

After installation, start the server:
```bash
frontend-dev-mcp
```

### Option 2: Local Development Setup

```bash
# Clone the repository
git clone https://github.com/Winds-AI/Frontend-development-mcp-tools.git
cd Frontend-development-mcp-tools

# Install and start
npm install && npm start
```

## 🔧 What Gets Installed

- ✅ **MCP Client**: Browser tools integration for AI IDEs
- ✅ **MCP Server**: WebSocket server for browser communication  
- ✅ **Chrome Extension**: Browser interaction interface
- ✅ **Auto-build**: TypeScript compilation and dependency resolution

## 🌐 Server Information

After installation, the server will:
- Start on port **3025** (or next available port)
- Auto-detect port conflicts
- Provide connection health monitoring
- Support multiple concurrent AI IDE connections

## 📱 Chrome Extension Setup

1. Open Chrome and go to `chrome://extensions/`
2. Enable **Developer Mode** (top-right toggle)
3. Click **"Load unpacked"**
4. Select the `chrome-extension` folder from:
   - **Global install**: Check installation output for path
   - **Local install**: `./chrome-extension` in project directory

## 🤖 AI IDE Configuration

### For Windsurf IDE
Add to your MCP configuration:

```json
{
  "mcpServers": {
    "browser-tools-frontend-dev": {
      "command": "frontend-development-mcp",
      "env": {
        "SWAGGER_URL": "https://api.example.com/docs/swagger.json",
        "PROJECT_ROOT": "/path/to/your/project",
        "AUTH_ORIGIN": "http://localhost:5173",
        "AUTH_STORAGE_TYPE": "localStorage",
        "AUTH_TOKEN_KEY": "authToken",
        "API_BASE_URL": "https://api.example.com"
      }
    }
  }
}
```

### For Cursor IDE
Add similar configuration to your `.cursorrules` file.

## 🧪 Testing Installation

```bash
# Test server connection
curl http://localhost:3025/connection-health

# Or use the built-in test
npm test
```

## 🔍 Troubleshooting

### Server Won't Start
- Check if port 3025-3035 are available
- Ensure Node.js version >= 18.0.0
- Try restarting with `npm start`

### Chrome Extension Issues
- Reload the extension in `chrome://extensions/`
- Check DevTools console for errors
- Ensure server is running before connecting

### AI IDE Connection Problems
- Restart your AI IDE after configuration changes
- Verify MCP configuration file syntax
- Check server logs for connection attempts

## 📖 Full Documentation

- **Setup Guide**: `SETUP_GUIDE.md`
- **Usage Guide**: `docs/HOW_TO_USE.md`
- **Project Overview**: `docs/PROJECT_OVERVIEW.md`
- **Future Plans**: `docs/FUTURE_PLANS.md`

## 🆘 Support

- **Issues**: [GitHub Issues](https://github.com/Winds-AI/Frontend-development-mcp-tools/issues)
- **Discussions**: [GitHub Discussions](https://github.com/Winds-AI/Frontend-development-mcp-tools/discussions)
- **LinkedIn**: [Meet Limbani](https://www.linkedin.com/in/meet-limbani/)

---

**Happy AI-powered development! 🚀**
