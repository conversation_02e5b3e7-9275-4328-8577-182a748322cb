#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

console.log('🚀 Starting Frontend Development MCP Tools...\n');

// Check if built files exist
const mcpServerPath = join(rootDir, 'browser-tools-mcp', 'dist', 'mcp-server.js');
const browserServerPath = join(rootDir, 'browser-tools-server', 'dist', 'browser-connector.js');

if (!fs.existsSync(mcpServerPath) || !fs.existsSync(browserServerPath)) {
    console.log('📦 Building projects...');
    const buildProcess = spawn('npm', ['run', 'build'], {
        cwd: rootDir,
        stdio: 'inherit',
        shell: true
    });

    buildProcess.on('close', (code) => {
        if (code === 0) {
            console.log('✅ Build completed successfully!\n');
            startServer();
        } else {
            console.error('❌ Build failed with code:', code);
            process.exit(1);
        }
    });
} else {
    startServer();
}

function startServer() {
    console.log('🌐 Starting browser tools server...');
    console.log('📍 Server will be available at: http://localhost:3025 (or next available port)');
    console.log('🔧 Chrome Extension: Load the chrome-extension folder in Chrome Developer Mode');
    console.log('📁 Chrome Extension Location:', join(rootDir, 'chrome-extension'));
    console.log('📖 Setup Guide: See SETUP_GUIDE.md for AI IDE configuration\n');

    const serverProcess = spawn('node', ['dist/browser-connector.js'], {
        cwd: join(rootDir, 'browser-tools-server'),
        stdio: 'inherit',
        shell: true
    });

    // Handle graceful shutdown
    process.on('SIGINT', () => {
        console.log('\n🛑 Shutting down server...');
        serverProcess.kill('SIGINT');
        process.exit(0);
    });

    process.on('SIGTERM', () => {
        console.log('\n🛑 Shutting down server...');
        serverProcess.kill('SIGTERM');
        process.exit(0);
    });

    serverProcess.on('close', (code) => {
        if (code !== 0) {
            console.error(`❌ Server exited with code: ${code}`);
        }
        process.exit(code);
    });

    serverProcess.on('error', (error) => {
        console.error('❌ Failed to start server:', error);
        process.exit(1);
    });
}
