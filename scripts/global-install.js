#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🚀 Frontend Development MCP Tools - Global Installation\n');

// Get the installation directory (where this package is installed globally)
const packageDir = path.resolve(__dirname, '..');

console.log('📦 Installing dependencies for global package...');
console.log('📍 Package location:', packageDir);

async function runCommand(command, args, cwd) {
    return new Promise((resolve, reject) => {
        const process = spawn(command, args, {
            cwd,
            stdio: 'inherit',
            shell: true
        });

        process.on('close', (code) => {
            if (code === 0) {
                resolve();
            } else {
                reject(new Error(`Command failed with code: ${code}`));
            }
        });

        process.on('error', reject);
    });
}

async function install() {
    try {
        const mcpPath = join(packageDir, 'browser-tools-mcp');
        const serverPath = join(packageDir, 'browser-tools-server');
        
        // Check if directories exist
        if (!fs.existsSync(mcpPath)) {
            console.error('❌ MCP client directory not found:', mcpPath);
            process.exit(1);
        }
        
        if (!fs.existsSync(serverPath)) {
            console.error('❌ MCP server directory not found:', serverPath);
            process.exit(1);
        }
        
        console.log('🔧 Installing MCP client dependencies...');
        await runCommand('npm', ['install'], mcpPath);
        
        console.log('🔧 Installing MCP server dependencies...');
        await runCommand('npm', ['install'], serverPath);
        
        console.log('🏗️  Building MCP client...');
        await runCommand('npm', ['run', 'build'], mcpPath);
        
        console.log('🏗️  Building MCP server...');
        await runCommand('npm', ['run', 'build'], serverPath);
        
        console.log('\n✅ Global installation completed successfully!');
        console.log('\n🚀 Quick Start:');
        console.log('   frontend-dev-mcp             # Start the server');
        console.log('\n📖 Next Steps:');
        console.log('   1. Load chrome-extension folder in Chrome Developer Mode');
        console.log('   2. Configure your AI IDE (see documentation)');
        console.log('   3. Start developing with AI assistance!');
        console.log('\n📁 Chrome Extension Location:');
        console.log('  ', join(packageDir, 'chrome-extension'));
        
    } catch (error) {
        console.error('❌ Global installation failed:', error.message);
        process.exit(1);
    }
}

install();
