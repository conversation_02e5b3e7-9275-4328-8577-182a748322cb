#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

console.log('📦 Installing Frontend Development MCP Tools...\n');

async function runCommand(command, args, cwd) {
    return new Promise((resolve, reject) => {
        const process = spawn(command, args, {
            cwd,
            stdio: 'inherit',
            shell: true
        });

        process.on('close', (code) => {
            if (code === 0) {
                resolve();
            } else {
                reject(new Error(`Command failed with code: ${code}`));
            }
        });

        process.on('error', reject);
    });
}

async function install() {
    try {
        console.log('🔧 Installing MCP client dependencies...');
        await runCommand('npm', ['install'], join(rootDir, 'browser-tools-mcp'));
        
        console.log('🔧 Installing MCP server dependencies...');
        await runCommand('npm', ['install'], join(rootDir, 'browser-tools-server'));
        
        console.log('🏗️  Building MCP client...');
        await runCommand('npm', ['run', 'build'], join(rootDir, 'browser-tools-mcp'));
        
        console.log('🏗️  Building MCP server...');
        await runCommand('npm', ['run', 'build'], join(rootDir, 'browser-tools-server'));
        
        console.log('\n✅ Installation completed successfully!');
        console.log('\n🚀 Quick Start:');
        console.log('   npm start                    # Start the server');
        console.log('   npm run start:all           # Start with detailed output');
        console.log('\n📖 Next Steps:');
        console.log('   1. Load chrome-extension folder in Chrome Developer Mode');
        console.log('   2. Configure your AI IDE (see SETUP_GUIDE.md)');
        console.log('   3. Start developing with AI assistance!');
        
    } catch (error) {
        console.error('❌ Installation failed:', error.message);
        process.exit(1);
    }
}

install();
